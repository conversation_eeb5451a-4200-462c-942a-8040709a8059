'use client'

import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>pography, IconButton, Badge, Tooltip, Menu, MenuItem, ListItemIcon, ListItemText, Divider } from '@mui/material';
import NotificationsIcon from '@mui/icons-material/Notifications';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import SettingsIcon from '@mui/icons-material/Settings';
import LogoutIcon from '@mui/icons-material/Logout';
import CloudIcon from '@mui/icons-material/Cloud';
import ConfirmationNumberIcon from '@mui/icons-material/ConfirmationNumber';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import CodeIcon from '@mui/icons-material/Code';
import QuestionAnswerIcon from '@mui/icons-material/QuestionAnswer';
import KeyboardIcon from '@mui/icons-material/Keyboard';
import InfoIcon from '@mui/icons-material/Info';
import NewReleasesIcon from '@mui/icons-material/NewReleases';
import Image from 'next/image';
import Link from 'next/link';
import SupportIcon from './common/SupportIcon';

const Header = () => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [helpAnchorEl, setHelpAnchorEl] = useState(null);

  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleHelpMenu = (event) => {
    setHelpAnchorEl(event.currentTarget);
  };

  const handleHelpClose = () => {
    setHelpAnchorEl(null);
  };

  return (
    <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
      <Toolbar>
        <div style={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
          <Image
            src="/header_logo.png"
            alt="AlgoNav Logo"
            width={128}
            height={32}
            style={{ width: 'auto', height: '32px' }}
            priority
          />
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginLeft: '16px' }}>
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{
                color: 'white',
                fontWeight: 500
              }}
            >
              Positioning Suite
            </Typography>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                backgroundColor: 'rgba(255, 255, 255, 0.12)',
                paddingLeft: '12px',
                paddingRight: '12px',
                paddingTop: '4px',
                paddingBottom: '4px',
                borderRadius: '12px',
                border: '1px solid rgba(255, 255, 255, 0.25)'
              }}
            >
              <CloudIcon
                sx={{
                  color: 'rgba(255, 255, 255, 0.8)',
                  fontSize: '0.9rem'
                }}
              />
              <Typography
                variant="body2"
                noWrap
                component="div"
                sx={{
                  color: 'rgba(255, 255, 255, 0.8)',
                  fontWeight: 400,
                  fontSize: '0.9rem'
                }}
              >
                LocalCloud
              </Typography>
            </div>
          </div>
        </div>
        <div style={{ display: 'flex', gap: '12px' }}>
          <Tooltip title="Help">
            <IconButton
              color="inherit"
              onClick={handleHelpMenu}
              aria-controls="help-menu"
              aria-haspopup="true"
            >
              <SupportIcon width={24} height={24} />
            </IconButton>
          </Tooltip>
          <Tooltip title="Notifications">
            <IconButton color="inherit">
              <Badge badgeContent={2} color="secondary">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Tooltip>
          <Tooltip title="Account">
            <IconButton
              color="inherit"
              onClick={handleMenu}
              aria-controls="account-menu"
              aria-haspopup="true"
            >
              <AccountCircleIcon />
            </IconButton>
          </Tooltip>
          <Menu
            id="account-menu"
            anchorEl={anchorEl}
            keepMounted
            open={Boolean(anchorEl)}
            onClose={handleClose}
          >
            <MenuItem onClick={handleClose}>
              <ListItemIcon>
                <AccountCircleIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Profile" />
            </MenuItem>
            <MenuItem onClick={handleClose}>
              <ListItemIcon>
                <SettingsIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Settings" />
            </MenuItem>
            <MenuItem onClick={handleClose}>
              <ListItemIcon>
                <LogoutIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Logout" />
            </MenuItem>
          </Menu>
          <Menu
            id="help-menu"
            anchorEl={helpAnchorEl}
            keepMounted
            open={Boolean(helpAnchorEl)}
            onClose={handleHelpClose}
          >
            <MenuItem onClick={handleHelpClose}>
              <ListItemIcon>
                <MenuBookIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Documentation" />
            </MenuItem>
            <MenuItem onClick={handleHelpClose}>
              <ListItemIcon>
                <CodeIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="API Documentation" />
            </MenuItem>
            <MenuItem onClick={handleHelpClose}>
              <ListItemIcon>
                <SupportIcon width={20} height={20} />
              </ListItemIcon>
              <ListItemText primary="User Guide" />
            </MenuItem>
            <MenuItem onClick={handleHelpClose}>
              <ListItemIcon>
                <QuestionAnswerIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="FAQ" />
            </MenuItem>
            <MenuItem onClick={handleHelpClose}>
              <ListItemIcon>
                <KeyboardIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Keyboard Shortcuts" />
            </MenuItem>
            <Divider />
            <MenuItem onClick={handleHelpClose}>
              <ListItemIcon>
                <NewReleasesIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Release Notes" />
            </MenuItem>
            <MenuItem onClick={handleHelpClose}>
              <ListItemIcon>
                <InfoIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="About" />
            </MenuItem>
            <Divider />
            <Link href="/tickets" passHref style={{ textDecoration: 'none', color: 'inherit' }}>
              <MenuItem onClick={handleHelpClose}>
                <ListItemIcon>
                  <ConfirmationNumberIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Support Tickets" />
              </MenuItem>
            </Link>
          </Menu>
        </div>
      </Toolbar>
    </AppBar>
  );
};

export default Header;