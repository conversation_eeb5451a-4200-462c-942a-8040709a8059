import React from 'react';
import {
  <PERSON>con<PERSON><PERSON>on,
  Toolt<PERSON>,
  Stack,
  Chip,
  Button,
  Box,
  CircularProgress
} from '@mui/material';
import SupportIcon from './SupportIcon';
import {
  useTicketsByTarget,
  getTicketStatusColor,
  getTicketStatusText
} from '../../lib/hooks/useTicketsByTarget';

interface SupportIconWithStatusProps {
  targetType: 'job' | 'task' | 'dataset';
  targetId: number;
  onTicketClick: (ticket: any) => void;
  onCreateTicket: () => void;
  variant?: 'icon' | 'button'; // icon for TaskRow, button for JobHeader
  size?: 'small' | 'medium';
  className?: string;
  jobOnly?: boolean; // For job headers to only show job-specific tickets
}

const SupportIconWithStatus: React.FC<SupportIconWithStatusProps> = ({
  targetType,
  targetId,
  onTicketClick,
  onCreateTicket,
  variant = 'icon',
  size = 'small',
  className = '',
  jobOnly = false
}) => {
  const { data: ticket, isLoading } = useTicketsByTarget(targetType, targetId, jobOnly);
  const hasTicket = !!ticket;
  const status = ticket?.status || null;

  const handleClick = () => {
    if (hasTicket && ticket) {
      // Pass the full ticket object to the click handler
      onTicketClick(ticket);
    } else {
      onCreateTicket();
    }
  };

  const getIconColor = () => {
    if (isLoading) return 'text.secondary';
    if (!hasTicket) return 'text.secondary';

    switch (status) {
      case 'open':
        return 'warning.main';
      case 'waiting_on_customer':
        return 'info.main';
      case 'resolved':
        return 'success.main';
      case 'closed':
        return 'text.secondary';
      default:
        return 'text.secondary';
    }
  };

  const getSvgIconColor = () => {
    if (isLoading) return '#000000';
    if (!hasTicket) return '#000000'; // Default black color

    switch (status) {
      case 'open':
        return '#ed6c02'; // warning.main
      case 'waiting_on_customer':
        return '#0288d1'; // info.main
      case 'resolved':
        return '#2e7d32'; // success.main
      case 'closed':
        return '#000000'; // Default black
      default:
        return '#000000'; // Default black
    }
  };

  const getTooltipText = () => {
    if (isLoading) return 'Loading ticket status...';
    if (!hasTicket) return `Request support for this ${targetType}`;
    return `View ticket (${getTicketStatusText(status)})`;
  };

  if (variant === 'button') {
    // Button variant for JobHeader
    return (
      <Stack direction="row" spacing={1} alignItems="center">
        <Button
          variant="outlined"
          color={hasTicket && status && getTicketStatusColor(status) !== 'default' ? getTicketStatusColor(status) as any : 'primary'}
          size={size}
          startIcon={
            isLoading ? (
              <CircularProgress size={16} />
            ) : (
              <Box
                component="span"
                sx={{
                  display: 'inline-flex',
                  color: getSvgIconColor(),
                  '& img': {
                    filter: getSvgIconColor() !== '#000000'
                      ? `brightness(0) saturate(100%) ${
                          getSvgIconColor() === '#ed6c02'
                            ? 'invert(58%) sepia(96%) saturate(1352%) hue-rotate(21deg) brightness(95%) contrast(90%)'
                          : getSvgIconColor() === '#0288d1'
                            ? 'invert(47%) sepia(99%) saturate(1056%) hue-rotate(196deg) brightness(101%) contrast(101%)'
                          : getSvgIconColor() === '#2e7d32'
                            ? 'invert(19%) sepia(100%) saturate(5016%) hue-rotate(115deg) brightness(97%) contrast(80%)'
                            : 'none'
                        }`
                      : 'none'
                  }
                }}
              >
                <SupportIcon width={16} height={16} />
              </Box>
            )
          }
          onClick={handleClick}
          disabled={isLoading}
          sx={{
            color: getIconColor(),
            borderColor: getIconColor(),
            '&:hover': {
              borderColor: getIconColor(),
              backgroundColor: 'rgba(0, 0, 0, 0.04)'
            }
          }}
        >
          {hasTicket ? 'View Ticket' : 'Request Support'}
        </Button>
        
        {hasTicket && status && (
          <Chip
            label={getTicketStatusText(status)}
            color={getTicketStatusColor(status) as any}
            size="small"
            variant="outlined"
          />
        )}
      </Stack>
    );
  }

  // Icon variant for TaskRow
  return (
    <Stack direction="row" spacing={1} alignItems="center">
      <Tooltip title={getTooltipText()}>
        <span>
          <IconButton
            size={size}
            onClick={handleClick}
            disabled={isLoading}
            aria-label={getTooltipText()}
            sx={{
              color: getIconColor(),
              '&:hover': {
                color: hasTicket ? getIconColor() : 'primary.main'
              }
            }}
            className={className}
          >
            {isLoading ? (
              <CircularProgress size={20} />
            ) : (
              <Box
                component="span"
                sx={{
                  display: 'inline-flex',
                  color: getSvgIconColor(),
                  '& img': {
                    filter: getSvgIconColor() !== '#000000'
                      ? `brightness(0) saturate(100%) ${
                          getSvgIconColor() === '#ed6c02'
                            ? 'invert(58%) sepia(96%) saturate(1352%) hue-rotate(21deg) brightness(95%) contrast(90%)'
                          : getSvgIconColor() === '#0288d1'
                            ? 'invert(47%) sepia(99%) saturate(1056%) hue-rotate(196deg) brightness(101%) contrast(101%)'
                          : getSvgIconColor() === '#2e7d32'
                            ? 'invert(19%) sepia(100%) saturate(5016%) hue-rotate(115deg) brightness(97%) contrast(80%)'
                            : 'none'
                        }`
                      : 'none'
                  }
                }}
              >
                <SupportIcon width={20} height={20} />
              </Box>
            )}
          </IconButton>
        </span>
      </Tooltip>
      
      {hasTicket && status && (
        <Chip
          label={getTicketStatusText(status)}
          color={getTicketStatusColor(status) as any}
          size="small"
          variant="outlined"
          sx={{ 
            fontSize: '0.75rem',
            height: '20px',
            '& .MuiChip-label': {
              px: 1
            }
          }}
        />
      )}
    </Stack>
  );
};

export default SupportIconWithStatus;
